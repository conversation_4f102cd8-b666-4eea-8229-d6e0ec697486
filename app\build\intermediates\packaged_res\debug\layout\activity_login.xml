<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/white">

    <!-- 应用图标 -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@mipmap/ic_launcher"
        android:contentDescription="@string/app_name" />

    <!-- 标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_title"
        android:textSize="24sp"
        android:textColor="@color/text_dark"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- 副标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请输入密码以继续使用浏览器"
        android:textSize="16sp"
        android:textColor="@color/text_light"
        android:layout_marginBottom="32dp" />

    <!-- 密码输入框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        style="@style/EditTextStyle"
        app:boxStrokeColor="@color/primary_blue"
        app:hintTextColor="@color/primary_blue">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/password_hint"
            android:inputType="textPassword"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 错误提示 -->
    <TextView
        android:id="@+id/textViewError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/password_error"
        android:textColor="@color/error_red"
        android:textSize="14sp"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <!-- 登录按钮 -->
    <Button
        android:id="@+id/buttonLogin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/login_button"
        style="@style/ButtonStyle"
        android:layout_marginTop="16dp" />

</LinearLayout>
