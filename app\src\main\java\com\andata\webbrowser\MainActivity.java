package com.andata.webbrowser;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private EditText editTextUrl;
    private Button buttonGo;
    private ImageButton buttonBack, buttonForward, buttonRefresh, buttonHome;
    private ProgressBar progressBar;
    
    private String defaultHomepage;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 获取默认首页
        defaultHomepage = getString(R.string.default_homepage);

        // 初始化视图
        initViews();
        
        // 配置WebView
        setupWebView();
        
        // 设置监听器
        setupListeners();
        
        // 加载默认首页
        loadUrl(defaultHomepage);
    }

    private void initViews() {
        webView = findViewById(R.id.webView);
        editTextUrl = findViewById(R.id.editTextUrl);
        buttonGo = findViewById(R.id.buttonGo);
        buttonBack = findViewById(R.id.buttonBack);
        buttonForward = findViewById(R.id.buttonForward);
        buttonRefresh = findViewById(R.id.buttonRefresh);
        buttonHome = findViewById(R.id.buttonHome);
        progressBar = findViewById(R.id.progressBar);
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // 启用JavaScript
        webSettings.setJavaScriptEnabled(true);
        
        // 启用DOM存储
        webSettings.setDomStorageEnabled(true);
        
        // 启用数据库存储
        webSettings.setDatabaseEnabled(true);
        
        // 启用应用缓存
//        webSettings.cache
        
        // 设置缓存模式
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 支持缩放
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 设置用户代理
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " WebBrowser/1.0");
        
        // 允许混合内容
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE);
        
        // 设置WebViewClient
        webView.setWebViewClient(new CustomWebViewClient());
        
        // 设置WebChromeClient
        webView.setWebChromeClient(new CustomWebChromeClient());
    }

    private void setupListeners() {
        // 访问按钮点击事件
        buttonGo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadUrlFromAddressBar();
            }
        });

        // 地址栏回车事件
        editTextUrl.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_GO || 
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    loadUrlFromAddressBar();
                    return true;
                }
                return false;
            }
        });

        // 后退按钮
        buttonBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (webView.canGoBack()) {
                    webView.goBack();
                }
            }
        });

        // 前进按钮
        buttonForward.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (webView.canGoForward()) {
                    webView.goForward();
                }
            }
        });

        // 刷新按钮
        buttonRefresh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                webView.reload();
            }
        });

        // 首页按钮
        buttonHome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadUrl(defaultHomepage);
            }
        });
    }

    private void loadUrlFromAddressBar() {
        String url = editTextUrl.getText().toString().trim();
        if (!url.isEmpty()) {
            loadUrl(url);
        }
    }

    private void loadUrl(String url) {
        // 处理URL格式
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            if (url.contains(".") && !url.contains(" ")) {
                // 看起来像域名，添加https://
                url = "https://" + url;
            } else {
                // 看起来像搜索词，使用百度搜索
                url = "https://www.baidu.com/s?wd=" + url;
            }
        }
        
        webView.loadUrl(url);
        editTextUrl.setText(url);
    }

    private void updateNavigationButtons() {
        buttonBack.setEnabled(webView.canGoBack());
        buttonForward.setEnabled(webView.canGoForward());
    }

    // 自定义WebViewClient
    private class CustomWebViewClient extends WebViewClient {
        @Override
        public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            progressBar.setVisibility(View.VISIBLE);
            editTextUrl.setText(url);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            progressBar.setVisibility(View.GONE);
            updateNavigationButtons();
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            Toast.makeText(MainActivity.this, getString(R.string.page_load_error) + ": " + description, 
                          Toast.LENGTH_SHORT).show();
        }
    }

    // 自定义WebChromeClient
    private class CustomWebChromeClient extends WebChromeClient {
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            progressBar.setProgress(newProgress);
            
            if (newProgress == 100) {
                progressBar.setVisibility(View.GONE);
            } else {
                progressBar.setVisibility(View.VISIBLE);
            }
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            // 可以在这里更新标题栏
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
