package com.andata.webbrowser;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;

public class LoginActivity extends AppCompatActivity {

    private static final String PREFS_NAME = "WebBrowserPrefs";
    private static final String KEY_IS_AUTHENTICATED = "is_authenticated";
    private static final String CORRECT_PASSWORD = "andata";

    private TextInputEditText editTextPassword;
    private Button buttonLogin;
    private TextView textViewError;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // 初始化SharedPreferences
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);

        // 检查是否已经验证过
        if (sharedPreferences.getBoolean(KEY_IS_AUTHENTICATED, false)) {
            // 已验证，直接跳转到主界面
            startMainActivity();
            return;
        }

        // 初始化视图
        initViews();
        setupListeners();
    }

    private void initViews() {
        editTextPassword = findViewById(R.id.editTextPassword);
        buttonLogin = findViewById(R.id.buttonLogin);
        textViewError = findViewById(R.id.textViewError);

        // 设置焦点到密码输入框
        editTextPassword.requestFocus();
    }

    private void setupListeners() {
        // 登录按钮点击事件
        buttonLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                attemptLogin();
            }
        });

        // 密码输入框回车事件
        editTextPassword.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_DONE || 
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    attemptLogin();
                    return true;
                }
                return false;
            }
        });

        // 密码输入框文本变化事件，隐藏错误提示
        editTextPassword.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    hideError();
                }
            }
        });
    }

    private void attemptLogin() {
        String password = editTextPassword.getText().toString().trim();

        if (password.isEmpty()) {
            showError("请输入密码");
            return;
        }

        if (CORRECT_PASSWORD.equals(password)) {
            // 密码正确
            loginSuccess();
        } else {
            // 密码错误
            loginFailed();
        }
    }

    private void loginSuccess() {
        // 保存验证状态
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_IS_AUTHENTICATED, true);
        editor.apply();

        // 显示成功提示
        Toast.makeText(this, getString(R.string.login_success), Toast.LENGTH_SHORT).show();

        // 跳转到主界面
        startMainActivity();
    }

    private void loginFailed() {
        // 显示错误提示
        showError(getString(R.string.password_error));
        
        // 清空密码输入框
        editTextPassword.setText("");
        editTextPassword.requestFocus();

        // 震动反馈（如果设备支持）
        editTextPassword.setError("密码错误");
    }

    private void showError(String message) {
        textViewError.setText(message);
        textViewError.setVisibility(View.VISIBLE);
    }

    private void hideError() {
        textViewError.setVisibility(View.GONE);
        editTextPassword.setError(null);
    }

    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish(); // 关闭登录界面，防止用户返回
    }

    @Override
    public void onBackPressed() {
        // 重写返回键，防止用户绕过验证
        moveTaskToBack(true);
    }
}
