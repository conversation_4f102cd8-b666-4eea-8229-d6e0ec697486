<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="url_input_background" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\drawable\url_input_background.xml" qualifiers="" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_blue">#FF2196F3</color><color name="primary_dark_blue">#FF1976D2</color><color name="accent_orange">#FFFF9800</color><color name="background_gray">#FFF5F5F5</color><color name="text_dark">#FF212121</color><color name="text_light">#FF757575</color><color name="error_red">#FFF44336</color><color name="success_green">#FF4CAF50</color></file><file path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">安全浏览器</string><string name="login_title">安全验证</string><string name="password_hint">请输入密码</string><string name="login_button">登录</string><string name="password_error">密码错误，请重试</string><string name="login_success">验证成功</string><string name="url_hint">输入网址或搜索</string><string name="go_button">访问</string><string name="back_button">后退</string><string name="forward_button">前进</string><string name="refresh_button">刷新</string><string name="home_button">首页</string><string name="loading">加载中...</string><string name="page_load_error">页面加载失败</string><string name="default_homepage">https://www.baidu.com</string></file><file path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.WebBrowser" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_dark_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowBackground">@color/background_gray</item>
    </style><style name="Theme.WebBrowser.Login" parent="Theme.WebBrowser">
        <item name="android:windowBackground">@color/white</item>
    </style><style name="ButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary_blue</item>
    </style><style name="EditTextStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_dark</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\augment-projects\webBrowser\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>