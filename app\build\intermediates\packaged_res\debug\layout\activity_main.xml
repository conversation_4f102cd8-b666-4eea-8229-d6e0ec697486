<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_gray">

    <!-- 工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/primary_blue"
        android:elevation="4dp">

        <!-- 导航按钮栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="8dp">

            <ImageButton
                android:id="@+id/buttonBack"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_media_previous"
                android:contentDescription="@string/back_button"
                android:tint="@color/white"
                android:layout_marginEnd="4dp" />

            <ImageButton
                android:id="@+id/buttonForward"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_media_next"
                android:contentDescription="@string/forward_button"
                android:tint="@color/white"
                android:layout_marginEnd="4dp" />

            <ImageButton
                android:id="@+id/buttonRefresh"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_popup_sync"
                android:contentDescription="@string/refresh_button"
                android:tint="@color/white"
                android:layout_marginEnd="4dp" />

            <ImageButton
                android:id="@+id/buttonHome"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@*android:drawable/ic_menu_home"
                android:contentDescription="@string/home_button"
                android:tint="@color/white" />

        </LinearLayout>

        <!-- 地址栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:paddingBottom="8dp">

            <EditText
                android:id="@+id/editTextUrl"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:hint="@string/url_hint"
                android:background="@drawable/url_input_background"
                android:paddingHorizontal="16dp"
                android:textSize="16sp"
                android:textColor="@color/text_dark"
                android:textColorHint="@color/text_light"
                android:inputType="textUri"
                android:imeOptions="actionGo"
                android:singleLine="true" />

            <Button
                android:id="@+id/buttonGo"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/go_button"
                android:textSize="14sp"
                android:layout_marginStart="8dp"
                style="@style/ButtonStyle" />

        </LinearLayout>

    </LinearLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:progressTint="@color/accent_orange"
        android:visibility="gone" />

    <!-- WebView -->
    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
