1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.andata.webbrowser"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络访问权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.andata.webbrowser.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.andata.webbrowser.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:9:5-38:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29e7c6ba7fab035deea6edc6edb758a5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="true"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:19:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:16:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.WebBrowser"
34-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:17:9-48
35        android:usesCleartextTraffic="true" >
35-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:18:9-44
36
37        <!-- 登录Activity作为启动Activity -->
38        <activity
38-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:23:9-31:20
39            android:name="com.andata.webbrowser.LoginActivity"
39-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:24:13-42
40            android:exported="true"
40-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:25:13-36
41            android:theme="@style/Theme.WebBrowser" >
41-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:26:13-52
42            <intent-filter>
42-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:27:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:28:17-69
43-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:29:17-77
45-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48
49        <!-- 主浏览器Activity -->
50        <activity
50-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:34:9-37:55
51            android:name="com.andata.webbrowser.MainActivity"
51-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:35:13-41
52            android:exported="false"
52-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:36:13-37
53            android:theme="@style/Theme.WebBrowser" />
53-->C:\Users\<USER>\Documents\augment-projects\webBrowser\app\src\main\AndroidManifest.xml:37:13-52
54
55        <provider
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.andata.webbrowser.androidx-startup"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f8b759739f7ef000a898a7b512acab2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a7a7678a1fb98d4f0332f55f2206cb8\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a7a7678a1fb98d4f0332f55f2206cb8\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a7a7678a1fb98d4f0332f55f2206cb8\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
65        </provider>
66    </application>
67
68</manifest>
