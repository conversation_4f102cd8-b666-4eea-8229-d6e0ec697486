{"logs": [{"outputFile": "com.andata.webbrowser.app-mergeDebugResources-26:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\550c3cc081a6141b2f229efed6041c29\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,8125", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,8204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\29e7c6ba7fab035deea6edc6edb758a5\\transformed\\core-1.9.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8209", "endColumns": "100", "endOffsets": "8305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\519ca36a42987cc2c500cb496aade669\\transformed\\material-1.9.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1204,1298,1374,1437,1549,1609,1674,1728,1798,1858,1914,2026,2083,2145,2201,2274,2408,2493,2578,2721,2805,2888,2977,3033,3088,3154,3227,3304,3388,3462,3538,3613,3686,3774,3847,3937,4028,4100,4174,4265,4317,4384,4468,4555,4617,4681,4744,4847,4944,5042,5139,5199,5258", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,88,55,54,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,59,58,76", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1199,1293,1369,1432,1544,1604,1669,1723,1793,1853,1909,2021,2078,2140,2196,2269,2403,2488,2573,2716,2800,2883,2972,3028,3083,3149,3222,3299,3383,3457,3533,3608,3681,3769,3842,3932,4023,4095,4169,4260,4312,4379,4463,4550,4612,4676,4739,4842,4939,5037,5134,5194,5253,5330"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,3612,3718,3844,3928,3994,4088,4164,4227,4339,4399,4464,4518,4588,4648,4704,4816,4873,4935,4991,5064,5198,5283,5368,5511,5595,5678,5767,5823,5878,5944,6017,6094,6178,6252,6328,6403,6476,6564,6637,6727,6818,6890,6964,7055,7107,7174,7258,7345,7407,7471,7534,7637,7734,7832,7929,7989,8048", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,88,55,54,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,59,58,76", "endOffsets": "412,3241,3330,3418,3516,3607,3713,3839,3923,3989,4083,4159,4222,4334,4394,4459,4513,4583,4643,4699,4811,4868,4930,4986,5059,5193,5278,5363,5506,5590,5673,5762,5818,5873,5939,6012,6089,6173,6247,6323,6398,6471,6559,6632,6722,6813,6885,6959,7050,7102,7169,7253,7340,7402,7466,7529,7632,7729,7827,7924,7984,8043,8120"}}]}]}