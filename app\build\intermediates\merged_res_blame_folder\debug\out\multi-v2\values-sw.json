{"logs": [{"outputFile": "com.andata.webbrowser.app-mergeDebugResources-26:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\29e7c6ba7fab035deea6edc6edb758a5\\transformed\\core-1.9.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8099", "endColumns": "100", "endOffsets": "8195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\550c3cc081a6141b2f229efed6041c29\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,8016", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,8094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\519ca36a42987cc2c500cb496aade669\\transformed\\material-1.9.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2806,2860,2913,2979,3051,3133,3223,3295,3370,3441,3514,3611,3685,3780,3877,3951,4036,4136,4189,4257,4345,4435,4497,4561,4624,4741,4851,4962,5074,5132,5189", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2801,2855,2908,2974,3046,3128,3218,3290,3365,3436,3509,3606,3680,3775,3872,3946,4031,4131,4184,4252,4340,4430,4492,4556,4619,4736,4846,4957,5069,5127,5184,5265"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3005,3081,3155,3228,3325,3414,3513,3642,3725,3793,3885,3958,4021,4107,4169,4232,4297,4365,4428,4482,4614,4671,4733,4787,4861,4999,5080,5160,5292,5377,5464,5552,5606,5659,5725,5797,5879,5969,6041,6116,6187,6260,6357,6431,6526,6623,6697,6782,6882,6935,7003,7091,7181,7243,7307,7370,7487,7597,7708,7820,7878,7935", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "304,3076,3150,3223,3320,3409,3508,3637,3720,3788,3880,3953,4016,4102,4164,4227,4292,4360,4423,4477,4609,4666,4728,4782,4856,4994,5075,5155,5287,5372,5459,5547,5601,5654,5720,5792,5874,5964,6036,6111,6182,6255,6352,6426,6521,6618,6692,6777,6877,6930,6998,7086,7176,7238,7302,7365,7482,7592,7703,7815,7873,7930,8011"}}]}]}