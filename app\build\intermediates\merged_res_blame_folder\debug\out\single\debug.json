[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\xml\\backup_rules.xml"}, {"merged": "com.andata.webbrowser.app-merged_res-28:/layout_activity_main.xml.flat", "source": "com.andata.webbrowser.app-main-30:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\drawable_url_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\drawable\\url_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-merged_res-28:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.andata.webbrowser.app-main-30:\\xml\\network_security_config.xml"}]