# 安全浏览器 (WebBrowser)

一款基于Java开发的Android网页浏览器应用，具有密码保护功能。

## 功能特性

### 🔐 安全验证
- 首次打开应用需要输入正确密码 `andata`
- 验证成功后，后续使用无需重复输入密码
- 使用SharedPreferences安全存储验证状态

### 🌐 网页浏览
- 完整的WebView浏览器功能
- 支持JavaScript和现代网页标准
- 智能地址栏：自动识别URL和搜索关键词
- 完整的导航控制：前进、后退、刷新、首页

### 🎨 用户界面
- Material Design设计风格
- 直观的操作界面
- 响应式布局适配不同屏幕尺寸
- 加载进度指示器

## 技术规格

- **开发语言**: Java
- **最低Android版本**: API 21 (Android 5.0)
- **目标Android版本**: API 34 (Android 14)
- **主要组件**: 
  - WebView (网页渲染)
  - SharedPreferences (数据存储)
  - Material Components (UI组件)

## 项目结构

```
app/
├── src/main/
│   ├── java/com/andata/webbrowser/
│   │   ├── LoginActivity.java      # 登录验证界面
│   │   └── MainActivity.java       # 主浏览器界面
│   ├── res/
│   │   ├── layout/                 # 界面布局文件
│   │   ├── values/                 # 资源值文件
│   │   ├── drawable/               # 图标和背景资源
│   │   └── xml/                    # 配置文件
│   └── AndroidManifest.xml         # 应用清单文件
└── build.gradle                    # 构建配置
```

## 安装和使用

### 编译要求
- Android Studio Arctic Fox 或更高版本
- JDK 8 或更高版本
- Android SDK API 34

### 编译步骤
1. 使用Android Studio打开项目
2. 等待Gradle同步完成
3. 连接Android设备或启动模拟器
4. 点击"Run"按钮编译并安装应用

### 使用说明
1. **首次启动**: 输入密码 `andata` 进行验证
2. **网页浏览**: 在地址栏输入网址或搜索关键词
3. **导航操作**: 使用工具栏按钮进行前进、后退、刷新等操作
4. **返回首页**: 点击首页按钮返回默认页面(百度)

## 安全特性

- **网络权限**: 仅申请必要的网络访问权限
- **数据保护**: 使用Android标准的数据存储机制
- **WebView安全**: 启用现代WebView安全特性
- **网络安全配置**: 支持HTTPS和安全的网络通信

## 开发说明

### 主要类说明

#### LoginActivity
- 处理用户密码验证
- 管理验证状态存储
- 提供友好的登录界面

#### MainActivity  
- 集成WebView浏览器功能
- 实现完整的浏览器控制
- 处理网页加载和导航

### 自定义配置
- 修改默认首页: 编辑 `strings.xml` 中的 `default_homepage`
- 修改验证密码: 编辑 `LoginActivity.java` 中的 `CORRECT_PASSWORD`
- 调整UI样式: 修改 `colors.xml` 和 `themes.xml`

## 许可证

本项目仅供学习和研究使用。

## 版本信息

- **版本**: 1.0
- **构建日期**: 2025年1月
- **开发者**: Andata Team
