<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.WebBrowser" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_dark_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_gray</item>
    </style>
    
    <!-- Login Activity Theme -->
    <style name="Theme.WebBrowser.Login" parent="Theme.WebBrowser">
        <item name="android:windowBackground">@color/white</item>
    </style>
    
    <!-- Button Styles -->
    <style name="ButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary_blue</item>
    </style>
    
    <!-- EditText Styles -->
    <style name="EditTextStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_dark</item>
    </style>
</resources>
